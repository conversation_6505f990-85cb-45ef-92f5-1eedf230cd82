import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/edit_command/edit_command_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';

void main() {
  group('EditCommandCubit', () {
    late EditCommandCubit editCommandCubit;
    late ValidateOrderCubit validateOrderCubit;

    setUp(() {
      validateOrderCubit = ValidateOrderCubit();
      editCommandCubit = EditCommandCubit(validateOrderCubit);
    });

    tearDown(() {
      editCommandCubit.close();
      validateOrderCubit.close();
    });

    test('initial state is correct', () {
      expect(editCommandCubit.state, const EditCommandState());
    });

    group('initialize', () {
      blocTest<EditCommandCubit, EditCommandState>(
        'should initialize with correct values',
        build: () => editCommandCubit,
        act: (cubit) => cubit.initialize(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
        ),
        expect: () => [
          const EditCommandState(
            stockCode: 'VPB',
            currentPrice: 25000.0,
            currentVolume: 1000,
            maxVolume: 10000,
            isInitialized: true,
          ),
        ],
      );
    });

    group('updatePrice', () {
      blocTest<EditCommandCubit, EditCommandState>(
        'should update price correctly',
        build: () => editCommandCubit,
        seed: () => const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ),
        act: (cubit) => cubit.updatePrice(26000.0),
        expect: () => [
          const EditCommandState(
            stockCode: 'VPB',
            currentPrice: 26000.0,
            currentVolume: 1000,
            maxVolume: 10000,
            isInitialized: true,
          ),
        ],
      );
    });

    group('updateVolume', () {
      blocTest<EditCommandCubit, EditCommandState>(
        'should update volume correctly',
        build: () => editCommandCubit,
        seed: () => const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ),
        act: (cubit) => cubit.updateVolume(1500),
        expect: () => [
          const EditCommandState(
            stockCode: 'VPB',
            currentPrice: 25000.0,
            currentVolume: 1500,
            maxVolume: 10000,
            isInitialized: true,
          ),
        ],
      );
    });

    group('adjustPrice', () {
      test('should increase price when amount is positive', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustPrice(100.0);
        expect(editCommandCubit.state.currentPrice, 25100.0);
      });

      test('should decrease price when amount is negative', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustPrice(-100.0);
        expect(editCommandCubit.state.currentPrice, 24900.0);
      });

      test('should not allow negative price', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 100.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustPrice(-200.0);
        expect(editCommandCubit.state.currentPrice, 100.0); // Should not change
      });
    });

    group('adjustVolume', () {
      test('should increase volume when amount is positive and within max', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustVolume(500);
        expect(editCommandCubit.state.currentVolume, 1500);
      });

      test('should decrease volume when amount is negative', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustVolume(-200);
        expect(editCommandCubit.state.currentVolume, 800);
      });

      test('should not exceed max volume', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 9500,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustVolume(1000);
        expect(editCommandCubit.state.currentVolume, 9500); // Should not change
      });

      test('should not allow negative volume', () {
        editCommandCubit.emit(const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 100,
          maxVolume: 10000,
          isInitialized: true,
        ));

        editCommandCubit.adjustVolume(-200);
        expect(editCommandCubit.state.currentVolume, 100); // Should not change
      });
    });

    group('reset', () {
      blocTest<EditCommandCubit, EditCommandState>(
        'should reset to initial state',
        build: () => editCommandCubit,
        seed: () => const EditCommandState(
          stockCode: 'VPB',
          currentPrice: 25000.0,
          currentVolume: 1000,
          maxVolume: 10000,
          isInitialized: true,
        ),
        act: (cubit) => cubit.reset(),
        expect: () => [const EditCommandState()],
      );
    });
  });
}
