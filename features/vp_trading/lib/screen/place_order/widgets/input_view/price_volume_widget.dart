import 'package:flutter/widgets.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/text_input_field.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/validator/validator_field_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/order_suggest/order_suggest_widget.dart';

class PriceVolumeWidget extends StatefulWidget {
  const PriceVolumeWidget({super.key});

  @override
  State<PriceVolumeWidget> createState() => _PriceVolumeWidgetState();
}

class _PriceVolumeWidgetState extends State<PriceVolumeWidget> {
  final _priceController = TextEditingController();
  final _volumeController = TextEditingController();
  final _priceKey = GlobalKey<TextInputFieldState>();

  @override
  dispose() {
    _priceController.dispose();
    _volumeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextInputField(
          key: _priceKey,
          priceController: _priceController,
          volumeController: _volumeController,
        ),
        const SizedBox(height: 8),
        const OrderSuggest(),
        ValidatorField(
          priceController: _priceController,
          volumeController: _volumeController,
        ),
        //   SizedBox(height: 8),
      ],
    );
  }
}

/// Reusable price/volume input widget that can be used in different contexts
/// Integrates with ValidateOrderCubit for validation and state management
class ReusablePriceVolumeWidget extends StatefulWidget {
  final TextEditingController? priceController;
  final TextEditingController? volumeController;
  final bool showOrderSuggest;
  final bool showValidation;
  final String? priceLabel;
  final String? volumeLabel;
  final String? priceHint;
  final String? volumeHint;
  final EdgeInsetsGeometry? padding;
  final TextInputFieldLayout layout;
  final double? spacing;

  const ReusablePriceVolumeWidget({
    super.key,
    this.priceController,
    this.volumeController,
    this.showOrderSuggest = true,
    this.showValidation = true,
    this.priceLabel,
    this.volumeLabel,
    this.priceHint,
    this.volumeHint,
    this.padding,
    this.layout = TextInputFieldLayout.horizontal,
    this.spacing,
  });

  @override
  State<ReusablePriceVolumeWidget> createState() =>
      _ReusablePriceVolumeWidgetState();
}

class _ReusablePriceVolumeWidgetState extends State<ReusablePriceVolumeWidget> {
  late TextEditingController _priceController;
  late TextEditingController _volumeController;
  late bool _shouldDisposeControllers;

  @override
  void initState() {
    super.initState();
    _shouldDisposeControllers =
        widget.priceController == null || widget.volumeController == null;
    _priceController = widget.priceController ?? TextEditingController();
    _volumeController = widget.volumeController ?? TextEditingController();
  }

  @override
  void dispose() {
    if (_shouldDisposeControllers) {
      _priceController.dispose();
      _volumeController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextInputField(
            priceController: _priceController,
            volumeController: _volumeController,
            layout: widget.layout,
            spacing: widget.spacing,
          ),
          if (widget.showOrderSuggest) ...[
            const SizedBox(height: 8),
            const OrderSuggest(),
          ],
          if (widget.showValidation) ...[
            ValidatorField(
              priceController: _priceController,
              volumeController: _volumeController,
            ),
          ],
        ],
      ),
    );
  }
}
