import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

/// Specialized input widget for edit command that integrates with validation
class EditCommandInputWidget extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final VoidCallback onDecrease;
  final VoidCallback onIncrease;
  final String? errorText;
  final bool isPrice;

  const EditCommandInputWidget({
    super.key,
    required this.label,
    required this.controller,
    required this.onDecrease,
    required this.onIncrease,
    this.errorText,
    this.isPrice = false,
  });

  @override
  State<EditCommandInputWidget> createState() => _EditCommandInputWidgetState();
}

class _EditCommandInputWidgetState extends State<EditCommandInputWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: context.textStyle.body14?.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  widget.isPrice
                      ? previous.currentPrice != current.currentPrice
                      : previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            final newValue =
                widget.isPrice
                    ? state.currentPrice ?? ''
                    : state.currentVolume ?? '';

            if (widget.controller.text != newValue) {
              widget.controller.text = newValue;
              widget.controller.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.controller.text.length),
              );
            }
          },
          child: VPTextField(
            controller: widget.controller,
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            style: context.textStyle.body14?.copyWith(color: Colors.white),
            inputType: InputType.rest,
            onChanged: (value) {
              if (widget.isPrice) {
                context.read<ValidateOrderCubit>().onChangePrice(value);
              } else {
                context.read<ValidateOrderCubit>().onChangeVolumne(value);
              }
            },
            prefixIcon:
                (color) => IconButton(
                  icon: Icon(
                    Icons.remove,
                    color: vpColor.iconPrimary,
                    size: 24,
                  ),
                  onPressed: widget.onDecrease,
                ),
            suffixIcon:
                (color) => IconButton(
                  icon: Icon(Icons.add, color: vpColor.iconPrimary, size: 24),
                  onPressed: widget.onIncrease,
                ),
            caption:
                widget.errorText != null
                    ? (color) => Text(
                      widget.errorText!,
                      style: context.textStyle.captionRegular?.copyWith(
                        color: context.colors.textAccentRed,
                      ),
                    )
                    : null,
          ),
        ),
      ],
    );
  }
}

/// Widget that displays max volume information
class MaxVolumeInfoWidget extends StatelessWidget {
  final int maxVolume;

  const MaxVolumeInfoWidget({super.key, required this.maxVolume});

  @override
  Widget build(BuildContext context) {
    return Text(
      '${VPTradingLocalize.current.trading_max_volume}: $maxVolume',
      style: context.textStyle.captionRegular?.copyWith(
        color: Colors.grey[600],
      ),
    );
  }
}
