import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class EditCommandBottomSheet extends StatefulWidget {
  final String stockCode;
  final double currentPrice;
  final int currentVolume;

  final int maxVolume;
  final Function(double newPrice, int newVolume) onConfirm;

  const EditCommandBottomSheet({
    super.key,
    required this.stockCode,
    required this.currentPrice,
    required this.currentVolume,
    required this.maxVolume,
    required this.onConfirm,
  });

  @override
  State<EditCommandBottomSheet> createState() => _EditCommandBottomSheetState();
}

class _EditCommandBottomSheetState extends State<EditCommandBottomSheet> {
  late TextEditingController _priceController;
  late TextEditingController _volumeController;
  String? _priceError;
  String? _volumeError;

  @override
  void initState() {
    super.initState();
    _priceController = TextEditingController(
      text: widget.currentPrice.toString().getPriceFormatted(),
    );
    _volumeController = TextEditingController(
      text: widget.currentVolume.toString(),
    );
  }

  @override
  void dispose() {
    _priceController.dispose();
    _volumeController.dispose();
    super.dispose();
  }

  void _adjustPrice(double amount) {
    final currentPrice = double.tryParse(_priceController.text) ?? 0;
    final newPrice = currentPrice + amount;
    if (newPrice > 0) {
      _priceController.text = newPrice.toString().getPriceFormatted();
    }
  }

  void _adjustVolume(int amount) {
    final currentVolume = int.tryParse(_volumeController.text) ?? 0;
    final newVolume = currentVolume + amount;
    if (newVolume > 0 && newVolume <= widget.maxVolume) {
      _volumeController.text = newVolume.toString();
    }
  }

  void _handleConfirm() {
    if (_priceError == null && _volumeError == null) {
      final newPrice = double.parse(_priceController.text);
      final newVolume = int.parse(_volumeController.text);
      widget.onConfirm(newPrice, newVolume);
      Navigator.pop(context);
    }
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required VoidCallback onDecrease,
    required VoidCallback onIncrease,
    String? errorText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyle.body14?.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        VPTextField(
          controller: controller,
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          style: context.textStyle.body14?.copyWith(color: Colors.white),
          inputType: InputType.rest,
          prefixIcon:
              (color) => IconButton(
                icon: Icon(Icons.remove, color: vpColor.iconPrimary, size: 24),
                onPressed: onDecrease,
              ),

          suffixIcon:
              (color) => IconButton(
                icon: Icon(Icons.add, color: vpColor.iconPrimary, size: 24),
                onPressed: onIncrease,
              ),
          caption:
              errorText != null
                  ? (color) => Text(
                    errorText,
                    style: context.textStyle.captionRegular?.copyWith(
                      color: context.colors.textAccentRed,
                    ),
                  )
                  : null,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        Text(
          VPTradingLocalize.current.trading_buy_order_title,
          style: context.textStyle.subtitle16?.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),

        VPTextField(
          hintText: widget.stockCode,
          textAlign: TextAlign.start,
          inputType: InputType.disabled,
        ),

        const SizedBox(height: 16),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              VPTradingLocalize.current.trading_buying_power,
              style: context.textStyle.body14?.copyWith(color: Colors.white),
            ),
            BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
              builder: (context, state) {
                if (state.availableTrade != null) {
                  return Text(
                    MoneyUtils.formatMoney(
                      (state.availableTrade!.pp0 ?? 0).toDouble(),
                    ),
                    style: context.textStyle.subtitle16?.copyWith(
                      color: Colors.white,
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ],
        ),
        const SizedBox(height: 24),

        _buildInputField(
          controller: _priceController,
          label: VPTradingLocalize.current.trading_price,
          onDecrease: () => _adjustPrice(-0.1),
          onIncrease: () => _adjustPrice(0.1),
          errorText: _priceError,
        ),
        const SizedBox(height: 16),

        _buildInputField(
          controller: _volumeController,
          label: VPTradingLocalize.current.trading_volume,
          onDecrease: () => _adjustVolume(-100),
          onIncrease: () => _adjustVolume(100),
          errorText: _volumeError,
        ),
        const SizedBox(height: 8),
        Text(
          '${VPTradingLocalize.current.trading_max_volume}: ${widget.maxVolume}',
          style: context.textStyle.captionRegular?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 24),

        VpsButton.primarySmall(
          title: VPTradingLocalize.current.trading_edit_order_button,
          onPressed: _handleConfirm,
          width: double.infinity,
        ),
      ],
    );
  }
}
