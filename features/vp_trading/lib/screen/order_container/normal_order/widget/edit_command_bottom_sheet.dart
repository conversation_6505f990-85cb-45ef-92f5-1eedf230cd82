import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/edit_command/edit_command_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_input_widget.dart';

class EditCommandBottomSheet extends StatefulWidget {
  final String stockCode;
  final double currentPrice;
  final int currentVolume;
  final int maxVolume;
  final Function(double newPrice, int newVolume) onConfirm;
  final StockInfoModel? stockInfo;
  final AvailableTradeModel? availableTrade;

  const EditCommandBottomSheet({
    super.key,
    required this.stockCode,
    required this.currentPrice,
    required this.currentVolume,
    required this.maxVolume,
    required this.onConfirm,
    this.stockInfo,
    this.availableTrade,
  });

  @override
  State<EditCommandBottomSheet> createState() => _EditCommandBottomSheetState();
}

class _EditCommandBottomSheetState extends State<EditCommandBottomSheet> {
  late TextEditingController _priceController;
  late TextEditingController _volumeController;
  late EditCommandCubit _editCommandCubit;
  late ValidateOrderCubit _validateOrderCubit;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _priceController = TextEditingController(
      text: widget.currentPrice.toString().getPriceFormatted(),
    );
    _volumeController = TextEditingController(
      text: widget.currentVolume.toString(),
    );

    // Initialize cubits
    _validateOrderCubit = ValidateOrderCubit();
    _editCommandCubit = EditCommandCubit(_validateOrderCubit);

    // Initialize edit command with current values
    _editCommandCubit.initialize(
      stockCode: widget.stockCode,
      currentPrice: widget.currentPrice,
      currentVolume: widget.currentVolume,
      maxVolume: widget.maxVolume,
      stockInfo: widget.stockInfo,
      availableTrade: widget.availableTrade,
    );
  }

  @override
  void dispose() {
    _priceController.dispose();
    _volumeController.dispose();
    _editCommandCubit.close();
    _validateOrderCubit.close();
    super.dispose();
  }

  void _adjustPrice(bool increase) {
    _validateOrderCubit.priceTap(
      text: _priceController.text,
      increase: increase,
    );
  }

  void _adjustVolume(int amount) {
    _validateOrderCubit.volumneTap(
      text: _volumeController.text,
      increase: amount > 0,
    );
  }

  void _handleConfirm() {
    if (_editCommandCubit.canConfirm) {
      final state = _editCommandCubit.state;
      widget.onConfirm(state.currentPrice, state.currentVolume);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _validateOrderCubit),
        BlocProvider.value(value: _editCommandCubit),
      ],
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),

          Text(
            VPTradingLocalize.current.trading_buy_order_title,
            style: context.textStyle.subtitle16?.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 16),

          VPTextField(
            hintText: widget.stockCode,
            textAlign: TextAlign.start,
            inputType: InputType.disabled,
          ),

          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                VPTradingLocalize.current.trading_buying_power,
                style: context.textStyle.body14?.copyWith(color: Colors.white),
              ),
              BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
                builder: (context, state) {
                  if (state.availableTrade != null) {
                    return Text(
                      MoneyUtils.formatMoney(
                        (state.availableTrade!.pp0 ?? 0).toDouble(),
                      ),
                      style: context.textStyle.subtitle16?.copyWith(
                        color: Colors.white,
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ],
          ),
          const SizedBox(height: 24),

          BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
            builder: (context, validateState) {
              return Column(
                children: [
                  EditCommandInputWidget(
                    controller: _priceController,
                    label: VPTradingLocalize.current.trading_price,
                    onDecrease: () => _adjustPrice(false),
                    onIncrease: () => _adjustPrice(true),
                    errorText:
                        validateState.errorPrice.isError
                            ? validateState.errorPrice.message
                            : null,
                    isPrice: true,
                  ),
                  const SizedBox(height: 16),

                  EditCommandInputWidget(
                    controller: _volumeController,
                    label: VPTradingLocalize.current.trading_volume,
                    onDecrease: () => _adjustVolume(-100),
                    onIncrease: () => _adjustVolume(100),
                    errorText:
                        validateState.errorVolume.isError
                            ? validateState.errorVolume.message(
                              _validateOrderCubit.maxVolume().toString(),
                            )
                            : null,
                    isPrice: false,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 8),
          MaxVolumeInfoWidget(maxVolume: widget.maxVolume),
          const SizedBox(height: 24),

          BlocBuilder<EditCommandCubit, EditCommandState>(
            builder: (context, state) {
              return VpsButton.primarySmall(
                title: VPTradingLocalize.current.trading_edit_order_button,
                onPressed: _editCommandCubit.canConfirm ? _handleConfirm : null,
                width: double.infinity,
              );
            },
          ),
        ],
      ),
    );
  }
}
