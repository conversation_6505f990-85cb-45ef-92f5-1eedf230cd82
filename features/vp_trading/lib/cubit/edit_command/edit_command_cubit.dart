import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';

part 'edit_command_state.dart';

/// Cubit for managing edit command bottom sheet state
/// Integrates with ValidateOrderCubit for validation logic
class EditCommandCubit extends Cubit<EditCommandState> {
  final ValidateOrderCubit _validateOrderCubit;

  EditCommandCubit(this._validateOrderCubit) : super(const EditCommandState());

  /// Initialize the edit command with current values
  void initialize({
    required String stockCode,
    required double currentPrice,
    required int currentVolume,
    required int maxVolume,
    StockInfoModel? stockInfo,
    AvailableTradeModel? availableTrade,
  }) {
    // Update validate order cubit with necessary parameters
    _validateOrderCubit.updateParam(
      stockInfo: stockInfo,
      availableTrade: availableTrade,
      action: OrderAction.buy, // Default to buy for edit command
    );

    emit(state.copyWith(
      stockCode: stockCode,
      currentPrice: currentPrice,
      currentVolume: currentVolume,
      maxVolume: maxVolume,
      isInitialized: true,
    ));

    // Set initial values in validation cubit
    _validateOrderCubit.onChangePrice(currentPrice.toString());
    _validateOrderCubit.onChangeVolumne(currentVolume.toString());
  }

  /// Update price value
  void updatePrice(double price) {
    emit(state.copyWith(currentPrice: price));
    _validateOrderCubit.onChangePrice(price.toString());
  }

  /// Update volume value
  void updateVolume(int volume) {
    emit(state.copyWith(currentVolume: volume));
    _validateOrderCubit.onChangeVolumne(volume.toString());
  }

  /// Adjust price by amount
  void adjustPrice(double amount) {
    final newPrice = state.currentPrice + amount;
    if (newPrice > 0) {
      updatePrice(newPrice);
    }
  }

  /// Adjust volume by amount
  void adjustVolume(int amount) {
    final newVolume = state.currentVolume + amount;
    if (newVolume > 0 && newVolume <= state.maxVolume) {
      updateVolume(newVolume);
    }
  }

  /// Check if current values are valid for confirmation
  bool get canConfirm {
    final validateState = _validateOrderCubit.state;
    return validateState.isValid && 
           validateState.errorPrice == ErrorPrice.none &&
           validateState.errorVolume == ErrorVolume.none;
  }

  /// Get current validation errors
  String? get priceError {
    final errorPrice = _validateOrderCubit.state.errorPrice;
    return errorPrice.isError ? errorPrice.message : null;
  }

  String? get volumeError {
    final errorVolume = _validateOrderCubit.state.errorVolume;
    return errorVolume.isError 
        ? errorVolume.message(_validateOrderCubit.maxVolume().toString())
        : null;
  }

  /// Reset to initial state
  void reset() {
    emit(const EditCommandState());
    _validateOrderCubit.clear();
  }

  @override
  Future<void> close() {
    // Don't close the validate order cubit as it might be used elsewhere
    return super.close();
  }
}
