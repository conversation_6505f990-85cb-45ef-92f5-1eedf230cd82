part of 'edit_command_cubit.dart';

/// State for edit command bottom sheet
class EditCommandState extends Equatable {
  final String stockCode;
  final double currentPrice;
  final int currentVolume;
  final int maxVolume;
  final bool isInitialized;

  const EditCommandState({
    this.stockCode = '',
    this.currentPrice = 0.0,
    this.currentVolume = 0,
    this.maxVolume = 0,
    this.isInitialized = false,
  });

  EditCommandState copyWith({
    String? stockCode,
    double? currentPrice,
    int? currentVolume,
    int? maxVolume,
    bool? isInitialized,
  }) {
    return EditCommandState(
      stockCode: stockCode ?? this.stockCode,
      currentPrice: currentPrice ?? this.currentPrice,
      currentVolume: currentVolume ?? this.currentVolume,
      maxVolume: maxVolume ?? this.maxVolume,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  @override
  List<Object?> get props => [
        stockCode,
        currentPrice,
        currentVolume,
        maxVolume,
        isInitialized,
      ];
}
